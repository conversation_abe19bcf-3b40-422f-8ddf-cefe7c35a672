{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "fs": "^0.0.1-security", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "pdf-lib": "^1.17.1", "qrcode": "^1.5.4", "twilio": "^4.23.0", "uuid": "^11.1.0", "xss-clean": "^0.1.4"}}